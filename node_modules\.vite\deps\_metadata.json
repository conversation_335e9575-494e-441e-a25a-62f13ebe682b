{"hash": "8757f33e", "configHash": "f9827d57", "lockfileHash": "eec41907", "browserHash": "dc239bc1", "optimized": {"react": {"src": "../../.pnpm/react@19.1.0/node_modules/react/index.js", "file": "react.js", "fileHash": "2a18a5dd", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@19.1.0_react@19.1.0/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "d9deb02b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "bce6a0e2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2532e49d", "needsInterop": true}, "@radix-ui/react-label": {"src": "../../.pnpm/@radix-ui+react-label@2.1.6_f5d76421f0b83349b902a67252a7f51f/node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "a1fde261", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../.pnpm/@radix-ui+react-slot@1.2.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "820e04af", "needsInterop": false}, "class-variance-authority": {"src": "../../.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c04e8654", "needsInterop": false}, "clsx": {"src": "../../.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "91eb6977", "needsInterop": false}, "lucide-react": {"src": "../../.pnpm/lucide-react@0.510.0_react@19.1.0/node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "59d58ea8", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@19.1.0_react@19.1.0/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "2dbff9e4", "needsInterop": true}, "tailwind-merge": {"src": "../../.pnpm/tailwind-merge@3.3.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "0e788118", "needsInterop": false}}, "chunks": {"chunk-LIEQ75ZC": {"file": "chunk-LIEQ75ZC.js"}, "chunk-VPBCTWMN": {"file": "chunk-VPBCTWMN.js"}, "chunk-OBBQL43Q": {"file": "chunk-OBBQL43Q.js"}, "chunk-PLUGHXRK": {"file": "chunk-PLUGHXRK.js"}, "chunk-DEX2RCYB": {"file": "chunk-DEX2RCYB.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}