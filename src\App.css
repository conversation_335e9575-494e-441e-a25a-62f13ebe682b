/* Custom animations and styles for the landing page */

/* Custom animations for the landing page */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

.animate-pulse-slow {
  animation: pulseGentle 3s ease-in-out infinite;
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes pulseGentle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Mobile-first responsive improvements */
@media (max-width: 768px) {
  .hero-title-mobile {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .cta-mobile {
    font-size: 1.1rem;
    padding: 1rem 2rem;
  }
}

/* Enhanced CTA button styles for better text fitting */
.cta-button-enhanced {
  word-wrap: break-word;
  hyphens: auto;
  line-height: 1.2;
}

@media (max-width: 640px) {
  .cta-button-enhanced {
    font-size: 0.875rem !important;
    padding: 1rem 0.75rem !important;
    min-height: 56px !important;
  }
}

@media (max-width: 480px) {
  .cta-button-enhanced {
    font-size: 0.8rem !important;
    padding: 0.875rem 0.5rem !important;
    min-height: 52px !important;
  }
}

@media (max-width: 360px) {
  .cta-button-enhanced {
    font-size: 0.75rem !important;
    padding: 0.75rem 0.25rem !important;
    min-height: 48px !important;
  }
}
