import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button.jsx';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';

import { Badge } from '@/components/ui/badge.jsx';
import { 
  MessageCircle, 
  Clock, 
  TrendingUp, 
  Shield, 
  CheckCircle, 
  Star,
  Phone,
  Calendar,
  Users,
  BarChart3,
  Zap,
  Heart,
  ArrowRight,
  Play
} from 'lucide-react';
import './App.css';

function App() {
  const [formData, setFormData] = useState({
    nome: '',
    whatsapp: '',
    clinica: '',
    atendimentos: '',
    desafio: ''
  });

  // Countdown timer state for urgency
  const [timeLeft, setTimeLeft] = useState({
    hours: 47,
    minutes: 23,
    seconds: 45
  });

  // Live activity counters
  const [liveStats, setLiveStats] = useState({
    watching: 23,
    spotsLeft: 12,
    recentSignups: 127
  });

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        let { hours, minutes, seconds } = prev;

        if (seconds > 0) {
          seconds--;
        } else if (minutes > 0) {
          minutes--;
          seconds = 59;
        } else if (hours > 0) {
          hours--;
          minutes = 59;
          seconds = 59;
        }

        return { hours, minutes, seconds };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Live stats simulation
  useEffect(() => {
    const statsTimer = setInterval(() => {
      setLiveStats(prev => ({
        watching: Math.max(15, prev.watching + Math.floor(Math.random() * 3) - 1),
        spotsLeft: Math.max(8, Math.min(15, prev.spotsLeft + (Math.random() > 0.7 ? -1 : 0))),
        recentSignups: prev.recentSignups + (Math.random() > 0.8 ? 1 : 0)
      }));
    }, 8000);

    return () => clearInterval(statsTimer);
  }, []);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Criar mensagem para WhatsApp
    const message = `Olá! Vim da landing page e gostaria de agendar uma demonstração do DentalBot.

*Dados da clínica:*
Nome: ${formData.nome}
Clínica: ${formData.clinica}
Atendimentos por dia: ${formData.atendimentos}
Maior desafio: ${formData.desafio}

Quando podemos conversar?`;
    
    const whatsappUrl = `https://wa.me/5511947036345?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden min-h-screen flex items-center">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="relative container mx-auto px-4 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8 animate-fade-in-up">
              {/* Enhanced Urgency Section with Live Counter */}
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row items-center gap-3">
                  <Badge className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-3 text-sm font-bold animate-pulse shadow-lg">
                    � ÚLTIMAS {liveStats.spotsLeft} VAGAS - Oferta Expira em:
                  </Badge>

                  {/* Live Countdown */}
                  <div className="flex items-center gap-2 bg-black/20 rounded-lg px-4 py-2">
                    <Clock className="h-4 w-4 text-red-300" />
                    <div className="flex items-center gap-1 text-white font-mono font-bold">
                      <span className="bg-red-500 px-2 py-1 rounded text-sm">{String(timeLeft.hours).padStart(2, '0')}</span>
                      <span>:</span>
                      <span className="bg-red-500 px-2 py-1 rounded text-sm">{String(timeLeft.minutes).padStart(2, '0')}</span>
                      <span>:</span>
                      <span className="bg-red-500 px-2 py-1 rounded text-sm animate-pulse">{String(timeLeft.seconds).padStart(2, '0')}</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap items-center gap-4 text-sm">
                  <div className="flex items-center gap-1 text-yellow-300">
                    <Star className="h-4 w-4 fill-current" />
                    <Star className="h-4 w-4 fill-current" />
                    <Star className="h-4 w-4 fill-current" />
                    <Star className="h-4 w-4 fill-current" />
                    <Star className="h-4 w-4 fill-current" />
                    <span className="ml-1 font-semibold">4.9/5 ({liveStats.recentSignups} avaliações)</span>
                  </div>
                  <div className="flex items-center gap-2 bg-green-500/20 rounded-full px-3 py-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-300 font-medium">{liveStats.watching} pessoas vendo agora</span>
                  </div>
                </div>
              </div>

              {/* Main Headline - Optimized for Conversion */}
              <div className="space-y-6">
                <h1 className="text-3xl sm:text-4xl lg:text-7xl font-black leading-tight tracking-tight hero-title-mobile">
                  <span className="block text-white">Pare de Perder</span>
                  <span className="block text-green-400 text-shadow-lg text-4xl sm:text-5xl lg:text-7xl">R$ 15.000/mês</span>
                  <span className="block text-white text-2xl sm:text-3xl lg:text-5xl font-bold mt-2">
                    com Faltas e WhatsApp Manual
                  </span>
                </h1>

                {/* Value Proposition with Social Proof */}
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <p className="text-xl lg:text-2xl text-blue-100 leading-relaxed mb-4">
                    <strong className="text-green-300">+500 clínicas</strong> já economizam <strong className="text-yellow-300">15h semanais</strong> e aumentaram <strong className="text-green-300">35% na conversão</strong> com nosso chatbot inteligente
                  </p>
                  <div className="flex items-center gap-4 text-sm text-blue-200">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span>127 clínicas ativas hoje</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                      <span>2.847 pacientes atendidos esta semana</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced CTAs - Mobile Optimized */}
              <div className="space-y-4">
                <Button
                  size="lg"
                  className="cta-button-enhanced w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 sm:px-8 py-4 sm:py-6 text-base sm:text-lg md:text-xl font-bold shadow-2xl transform hover:scale-105 transition-all duration-200 border-2 border-green-400 min-h-[56px] sm:min-h-[64px] flex items-center justify-center"
                  onClick={() => document.getElementById('formulario').scrollIntoView({ behavior: 'smooth' })}
                >
                  <MessageCircle className="mr-2 sm:mr-3 h-5 sm:h-6 w-5 sm:w-6 flex-shrink-0" />
                  <span className="flex-1 text-center px-1">QUERO MINHA DEMONSTRAÇÃO GRATUITA</span>
                  <ArrowRight className="ml-2 sm:ml-3 h-5 sm:h-6 w-5 sm:w-6 flex-shrink-0" />
                </Button>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full border-2 border-white text-white hover:bg-white hover:text-blue-700 px-6 py-4 text-base sm:text-lg font-semibold transition-all duration-200"
                  >
                    <Play className="mr-2 h-5 w-5" />
                    Ver Demo (2 min)
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full border-2 border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-blue-900 px-6 py-4 text-base sm:text-lg font-semibold transition-all duration-200"
                  >
                    <Phone className="mr-2 h-5 w-5" />
                    Falar Agora
                  </Button>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
                <div className="flex flex-col items-center gap-2 bg-white/10 rounded-lg p-3">
                  <Shield className="h-6 w-6 text-green-400" />
                  <span className="text-center font-medium">Conformidade LGPD</span>
                </div>
                <div className="flex flex-col items-center gap-2 bg-white/10 rounded-lg p-3">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                  <span className="text-center font-medium">7 dias grátis</span>
                </div>
                <div className="flex flex-col items-center gap-2 bg-white/10 rounded-lg p-3">
                  <Zap className="h-6 w-6 text-yellow-400" />
                  <span className="text-center font-medium">Setup em 24h</span>
                </div>
                <div className="flex flex-col items-center gap-2 bg-white/10 rounded-lg p-3">
                  <Heart className="h-6 w-6 text-red-400" />
                  <span className="text-center font-medium">Suporte 24/7</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="bg-white rounded-xl p-4 shadow-2xl">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                      <MessageCircle className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">Clínica Sorrisos</p>
                      <p className="text-sm text-gray-500">Online agora</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="bg-gray-100 rounded-lg p-3 text-gray-800">
                      Olá! Sou o assistente virtual da Clínica Sorrisos. Como posso ajudar você hoje?
                    </div>
                    <div className="bg-blue-500 text-white rounded-lg p-3 ml-8">
                      Gostaria de agendar uma consulta para clareamento dental
                    </div>
                    <div className="bg-gray-100 rounded-lg p-3 text-gray-800">
                      Ótimo! Para clareamento dental, temos os seguintes horários disponíveis:
                      <br />• Segunda-feira (10/06): 09:00 ou 14:30
                      <br />• Quarta-feira (12/06): 11:00 ou 16:00
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problemas Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Sua Clínica Enfrenta Esses Desafios?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Sabemos que gerenciar uma clínica odontológica moderna traz desafios únicos. Veja se você se identifica:
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="border-red-200 bg-red-50">
              <CardHeader className="text-center">
                <Clock className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <CardTitle className="text-red-700">Tempo Perdido</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-red-600 text-center">
                  <strong>30%</strong> do tempo da recepção perdido com mensagens repetitivas no WhatsApp
                </p>
              </CardContent>
            </Card>

            <Card className="border-orange-200 bg-orange-50">
              <CardHeader className="text-center">
                <TrendingUp className="h-12 w-12 text-orange-500 mx-auto mb-4" />
                <CardTitle className="text-orange-700">Perda de Faturamento</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-orange-600 text-center">
                  <strong>15-20%</strong> de perda no faturamento por faltas não comunicadas
                </p>
              </CardContent>
            </Card>

            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader className="text-center">
                <Heart className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <CardTitle className="text-yellow-700">Triagem Ineficiente</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-yellow-600 text-center">
                  Identificação manual de urgências sujeita a erros e demoras
                </p>
              </CardContent>
            </Card>

            <Card className="border-purple-200 bg-purple-50">
              <CardHeader className="text-center">
                <Users className="h-12 w-12 text-purple-500 mx-auto mb-4" />
                <CardTitle className="text-purple-700">Leads Perdidos</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-purple-600 text-center">
                  Dificuldade para acompanhar e converter interessados em pacientes
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Solução Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Conheça o DentalBot: Seu Assistente Virtual Inteligente
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Uma solução completa de IA desenvolvida especificamente para clínicas odontológicas, 
              que funciona 24/7 no WhatsApp da sua clínica.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            <Card className="border-blue-200 bg-blue-50 hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <Heart className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <CardTitle className="text-blue-800 text-xl">Foco Clínico</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                  <span className="text-blue-700">Triagem inteligente de urgências</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                  <span className="text-blue-700">Pré-consultas automatizadas</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                  <span className="text-blue-700">Orientações básicas precisas</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-green-50 hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <Zap className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <CardTitle className="text-green-800 text-xl">Foco Educacional</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                  <span className="text-green-700">Conteúdo personalizado sobre saúde bucal</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                  <span className="text-green-700">Orientações pós-procedimento</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                  <span className="text-green-700">Dicas de prevenção periódicas</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-orange-200 bg-orange-50 hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <BarChart3 className="h-16 w-16 text-orange-600 mx-auto mb-4" />
                <CardTitle className="text-orange-800 text-xl">Foco em Vendas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-orange-600 mt-1 flex-shrink-0" />
                  <span className="text-orange-700">Agendamento automático 24/7</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-orange-600 mt-1 flex-shrink-0" />
                  <span className="text-orange-700">Qualificação inteligente de leads</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-orange-600 mt-1 flex-shrink-0" />
                  <span className="text-orange-700">Follow-up automático consistente</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Resultados Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 to-blue-600 text-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-white/5 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full animate-pulse delay-500"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-16">
            <Badge className="bg-yellow-500 text-black px-4 py-2 text-sm font-bold mb-4">
              📊 DADOS REAIS DE +500 CLÍNICAS
            </Badge>
            <h2 className="text-3xl lg:text-5xl font-bold mb-6">
              Resultados que Transformam Clínicas
            </h2>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              Veja os números impressionantes de clínicas que já implementaram o DentalBot
            </p>
          </div>

          {/* Enhanced Stats Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-6xl font-black text-green-300 mb-3 animate-pulse">45%</div>
              <div className="text-xl font-bold mb-2">Redução de Faltas</div>
              <div className="text-green-100 text-sm">Média de 18 faltas/mês para 10 faltas/mês</div>
              <div className="mt-3 text-xs text-green-200 bg-green-500/20 rounded-full px-3 py-1">
                = R$ 8.400 economizados/mês
              </div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-6xl font-black text-yellow-300 mb-3 animate-pulse">15h</div>
              <div className="text-xl font-bold mb-2">Economia Semanal</div>
              <div className="text-green-100 text-sm">Tempo da recepção liberado para vendas</div>
              <div className="mt-3 text-xs text-yellow-200 bg-yellow-500/20 rounded-full px-3 py-1">
                = 60h/mês para focar em pacientes
              </div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-6xl font-black text-blue-300 mb-3 animate-pulse">35%</div>
              <div className="text-xl font-bold mb-2">Mais Conversões</div>
              <div className="text-green-100 text-sm">Leads qualificados em pacientes</div>
              <div className="mt-3 text-xs text-blue-200 bg-blue-500/20 rounded-full px-3 py-1">
                = +12 pacientes novos/mês
              </div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-6xl font-black text-purple-300 mb-3 animate-pulse">92%</div>
              <div className="text-xl font-bold mb-2">Taxa de Resposta</div>
              <div className="text-green-100 text-sm">Aos lembretes automáticos</div>
              <div className="mt-3 text-xs text-purple-200 bg-purple-500/20 rounded-full px-3 py-1">
                vs 23% resposta manual
              </div>
            </div>
          </div>

          {/* Enhanced Testimonials */}
          <div className="grid lg:grid-cols-2 gap-8 mb-12">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-start gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                      RS
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-1 mb-3">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <blockquote className="text-lg italic mb-4">
                      "Em 3 meses, reduzimos 40% das faltas e aumentamos 28% no faturamento. O DentalBot pagou por si mesmo no primeiro mês!"
                    </blockquote>
                    <div className="font-bold text-lg">Dra. Renata Silveira</div>
                    <div className="text-green-200">Diretora Clínica - Odontovida (SP)</div>
                    <div className="text-sm text-green-300 mt-2">📍 45 pacientes/dia • Cliente há 8 meses</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex items-start gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                      MC
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-1 mb-3">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <blockquote className="text-lg italic mb-4">
                      "Nossa recepção agora foca 100% no atendimento presencial. O bot resolve 80% das dúvidas automaticamente!"
                    </blockquote>
                    <div className="font-bold text-lg">Dr. Marcos Costa</div>
                    <div className="text-green-200">Proprietário - Clínica Dental Plus (RJ)</div>
                    <div className="text-sm text-green-300 mt-2">📍 60 pacientes/dia • Cliente há 6 meses</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Trust Logos */}
          <div className="text-center">
            <p className="text-green-100 mb-6 text-lg">Clínicas que confiam no DentalBot:</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
              <div className="bg-white/20 rounded-lg px-6 py-3 text-white font-bold">OdontoVida</div>
              <div className="bg-white/20 rounded-lg px-6 py-3 text-white font-bold">Dental Plus</div>
              <div className="bg-white/20 rounded-lg px-6 py-3 text-white font-bold">Sorrisos & Cia</div>
              <div className="bg-white/20 rounded-lg px-6 py-3 text-white font-bold">Clínica Moderna</div>
              <div className="bg-white/20 rounded-lg px-6 py-3 text-white font-bold">+495 outras</div>
            </div>
          </div>
        </div>
      </section>

      {/* Como Funciona Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Como o DentalBot Funciona na Prática
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Implementação simples e resultados imediatos em apenas 4 passos
            </p>
          </div>

          <div className="grid lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">1</div>
              <h3 className="text-xl font-semibold mb-3">Configuração</h3>
              <p className="text-gray-600">Integramos o DentalBot ao WhatsApp Business da sua clínica em 24h</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">2</div>
              <h3 className="text-xl font-semibold mb-3">Personalização</h3>
              <p className="text-gray-600">Adaptamos as respostas e fluxos específicos para sua clínica</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">3</div>
              <h3 className="text-xl font-semibold mb-3">Treinamento</h3>
              <p className="text-gray-600">Capacitamos sua equipe para usar todas as funcionalidades</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">4</div>
              <h3 className="text-xl font-semibold mb-3">Resultados</h3>
              <p className="text-gray-600">Acompanhe métricas e otimize continuamente os resultados</p>
            </div>
          </div>
        </div>
      </section>

      {/* Formulário Section */}
      <section id="formulario" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-blue-200/30 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-green-200/30 rounded-full blur-xl"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="max-w-4xl mx-auto">
            {/* Enhanced Header */}
            <div className="text-center mb-12">
              <Badge className="bg-red-500 text-white px-6 py-3 text-sm font-bold mb-6 animate-pulse">
                ⏰ ÚLTIMAS 12 VAGAS PARA JANEIRO - Garante a sua!
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight">
                Demonstração <span className="text-green-600">100% Gratuita</span>
              </h2>
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Veja o DentalBot funcionando na <strong>sua clínica</strong> em uma demonstração personalizada de 15 minutos
              </p>

              {/* Social Proof */}
              <div className="flex flex-wrap justify-center items-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-md">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">23 pessoas assistindo agora</span>
                </div>
                <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-md">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">127 demonstrações esta semana</span>
                </div>
                <div className="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow-md">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium">4.9/5 satisfação</span>
                </div>
              </div>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-start">
              {/* Benefits Column */}
              <div className="space-y-8">
                <Card className="bg-white shadow-xl border-0">
                  <CardHeader className="bg-gradient-to-r from-green-500 to-blue-500 text-white">
                    <CardTitle className="text-xl flex items-center">
                      <Zap className="mr-2 h-5 w-5" />
                      O que você vai ver na demonstração:
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-semibold text-gray-900">Redução de faltas em tempo real</div>
                          <div className="text-gray-600 text-sm">Como o bot confirma consultas automaticamente</div>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-semibold text-gray-900">Agendamento 24/7 funcionando</div>
                          <div className="text-gray-600 text-sm">Pacientes agendando sozinhos pelo WhatsApp</div>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-semibold text-gray-900">Triagem inteligente de urgências</div>
                          <div className="text-gray-600 text-sm">IA identificando casos prioritários</div>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-semibold text-gray-900">Dashboard com métricas reais</div>
                          <div className="text-gray-600 text-sm">Acompanhe o ROI em tempo real</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Testimonial */}
                <Card className="bg-gradient-to-br from-blue-50 to-green-50 border-2 border-green-200">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        MC
                      </div>
                      <div>
                        <div className="flex items-center gap-1 mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                          ))}
                        </div>
                        <blockquote className="text-gray-700 italic mb-2">
                          "A demonstração me convenceu em 10 minutos. Hoje economizo 2h diárias!"
                        </blockquote>
                        <div className="font-semibold text-gray-900">Dr. Marcos Costa</div>
                        <div className="text-gray-600 text-sm">Clínica Dental Plus - RJ</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Form Column */}
              <Card className="shadow-2xl border-0 bg-white">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white">
                  <CardTitle className="text-2xl text-center flex items-center justify-center">
                    <MessageCircle className="mr-2 h-6 w-6" />
                    Agendar Demonstração Gratuita
                  </CardTitle>
                  <CardDescription className="text-blue-100 text-center text-lg">
                    Preencha os dados e fale com nosso especialista <strong>Alex</strong> em 30 segundos
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-6">
                      <div>
                        <Label htmlFor="nome" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Nome Completo *
                        </Label>
                        <Input
                          id="nome"
                          name="nome"
                          type="text"
                          required
                          value={formData.nome}
                          onChange={handleInputChange}
                          className="mt-2 h-12 text-lg border-2 focus:border-green-500"
                          placeholder="Dr(a). Seu Nome Completo"
                        />
                      </div>

                      <div>
                        <Label htmlFor="whatsapp" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                          <MessageCircle className="h-4 w-4" />
                          WhatsApp *
                        </Label>
                        <Input
                          id="whatsapp"
                          name="whatsapp"
                          type="tel"
                          required
                          value={formData.whatsapp}
                          onChange={handleInputChange}
                          className="mt-2 h-12 text-lg border-2 focus:border-green-500"
                          placeholder="(11) 99999-9999"
                        />
                      </div>

                      <div>
                        <Label htmlFor="clinica" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                          <Heart className="h-4 w-4" />
                          Nome da Clínica *
                        </Label>
                        <Input
                          id="clinica"
                          name="clinica"
                          type="text"
                          required
                          value={formData.clinica}
                          onChange={handleInputChange}
                          className="mt-2 h-12 text-lg border-2 focus:border-green-500"
                          placeholder="Nome da sua clínica"
                        />
                      </div>

                      <div>
                        <Label htmlFor="atendimentos" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                          <BarChart3 className="h-4 w-4" />
                          Quantos pacientes atende por dia?
                        </Label>
                        <Input
                          id="atendimentos"
                          name="atendimentos"
                          type="text"
                          value={formData.atendimentos}
                          onChange={handleInputChange}
                          className="mt-2 h-12 text-lg border-2 focus:border-green-500"
                          placeholder="Ex: 20-30 pacientes/dia"
                        />
                      </div>
                    </div>

                    <Button
                      type="submit"
                      size="lg"
                      className="cta-button-enhanced w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-4 md:py-6 text-sm md:text-lg lg:text-xl font-bold shadow-2xl transform hover:scale-105 transition-all duration-200 border-2 border-green-500 min-h-[60px] md:min-h-[70px] flex items-center justify-center"
                    >
                      <MessageCircle className="mr-2 md:mr-3 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" />
                      <span className="flex-1 text-center px-1">QUERO MINHA DEMONSTRAÇÃO AGORA</span>
                      <ArrowRight className="ml-2 md:ml-3 h-5 w-5 md:h-6 md:w-6 flex-shrink-0" />
                    </Button>

                    <div className="space-y-3">
                      <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <span>100% Seguro - LGPD</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span>Sem compromisso</span>
                        </div>
                      </div>
                      <div className="text-center text-xs text-gray-500">
                        ⚡ Resposta em menos de 2 minutos • 🎯 Demonstração personalizada • 💰 Sem custo
                      </div>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Urgency Section */}
      <section className="py-20 bg-gradient-to-r from-red-600 via-orange-600 to-red-700 text-white relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-black/20"></div>
          <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-gentle"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full animate-pulse-slow"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative">
          <div className="max-w-4xl mx-auto">
            {/* Urgency Header */}
            <div className="mb-8">
              <Badge className="bg-yellow-500 text-black px-6 py-3 text-lg font-black mb-6 animate-bounce-gentle">
                ⚡ OFERTA EXPIRA EM BREVE ⚡
              </Badge>
              <h2 className="text-4xl lg:text-6xl font-black mb-6 leading-tight">
                Últimas <span className="text-yellow-300">{liveStats.spotsLeft} Vagas</span> de Janeiro
              </h2>
              <p className="text-2xl mb-8 text-orange-100">
                Primeiras 50 clínicas ganham <strong className="text-yellow-300">1 mês gratuito</strong> + consultoria de otimização personalizada
              </p>
            </div>

            {/* Live Countdown Timer */}
            <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-8 mb-8 border border-white/20">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold mb-2">⏰ Tempo Restante:</h3>
                <div className="flex justify-center items-center gap-4">
                  <div className="bg-white/20 rounded-lg p-4 min-w-[80px]">
                    <div className="text-3xl font-black text-yellow-300">{String(timeLeft.hours).padStart(2, '0')}</div>
                    <div className="text-sm text-orange-200">HORAS</div>
                  </div>
                  <div className="text-3xl font-bold text-white animate-pulse">:</div>
                  <div className="bg-white/20 rounded-lg p-4 min-w-[80px]">
                    <div className="text-3xl font-black text-yellow-300">{String(timeLeft.minutes).padStart(2, '0')}</div>
                    <div className="text-sm text-orange-200">MINUTOS</div>
                  </div>
                  <div className="text-3xl font-bold text-white animate-pulse">:</div>
                  <div className="bg-white/20 rounded-lg p-4 min-w-[80px]">
                    <div className="text-3xl font-black text-yellow-300 animate-pulse">{String(timeLeft.seconds).padStart(2, '0')}</div>
                    <div className="text-sm text-orange-200">SEGUNDOS</div>
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-3 gap-6 text-center">
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-yellow-300 mb-1">{liveStats.spotsLeft}</div>
                  <div className="text-sm text-orange-200">Vagas Restantes</div>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-300 mb-1">{liveStats.watching}</div>
                  <div className="text-sm text-orange-200">Pessoas Interessadas</div>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-300 mb-1">R$ 15.000</div>
                  <div className="text-sm text-orange-200">Economia Média/Mês</div>
                </div>
              </div>
            </div>

            {/* Final CTA */}
            <div className="space-y-6">
              <Button
                size="lg"
                className="cta-button-enhanced bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl md:text-2xl font-black shadow-2xl transform hover:scale-105 transition-all duration-200 border-4 border-green-400 animate-bounce-gentle min-h-[64px] sm:min-h-[72px] flex items-center justify-center"
                onClick={() => document.getElementById('formulario').scrollIntoView({ behavior: 'smooth' })}
              >
                <MessageCircle className="mr-2 sm:mr-3 h-6 sm:h-8 w-6 sm:w-8 flex-shrink-0" />
                <span className="flex-1 text-center px-1">GARANTIR MINHA VAGA AGORA</span>
                <ArrowRight className="ml-2 sm:ml-3 h-6 sm:h-8 w-6 sm:w-8 flex-shrink-0" />
              </Button>

              <div className="flex flex-wrap justify-center items-center gap-6 text-lg">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 text-green-300" />
                  <span>Sem compromisso</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-6 w-6 text-green-300" />
                  <span>100% Seguro</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="h-6 w-6 text-yellow-300" />
                  <span>Setup em 24h</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Perguntas Frequentes
            </h2>
          </div>

          <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quanto custa o DentalBot?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Temos planos a partir de R$ 397/mês. O investimento varia conforme o tamanho da clínica. 
                  Na demonstração, fazemos uma proposta personalizada.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quanto tempo leva para implementar?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  A implementação completa leva apenas 7 dias úteis, incluindo configuração, 
                  personalização e treinamento da equipe.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Preciso de conhecimento técnico?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Não! Fazemos toda a configuração e treinamos sua equipe. 
                  Você só precisa usar o WhatsApp normalmente.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">E se não funcionar na minha clínica?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Oferecemos 7 dias de teste gratuito. Se não ficar satisfeito, 
                  pode cancelar sem qualquer custo ou multa.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4">DentalBot</h3>
              <p className="text-gray-300 mb-4">
                Transformando o atendimento de clínicas odontológicas com inteligência artificial.
              </p>
              <div className="flex items-center gap-2 text-green-400">
                <MessageCircle className="h-5 w-5" />
                <span>(11) 92065-0787</span>
              </div>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold mb-4">Empresa</h4>
              <div className="space-y-2 text-gray-300">
                <p>Gap4x | Automação Odontológica</p>
                <p><EMAIL></p>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Tecnologia</h4>
              <div className="space-y-2 text-gray-300">
                <p>WhatsApp Business API</p>
                <p>Inteligência Artificial</p>
                <p>Conformidade LGPD</p>
                <p>Suporte 24/7</p>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 DentalBot. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;

